# 窗体计算公式修改说明

## 修改概述

修复了窗体中单车道宽度和道路总宽之间的计算模式功能，实现了复杂的道路宽度计算公式。

## 修改内容

### 1. 控件状态管理

#### 选择"以单车道宽度计算"时：
- **单车道宽度 (textBox2)**: 可编辑状态
- **道路总宽 (textBox5)**: 不可编辑状态（灰色）
- **自动计算**: 道路总宽根据单车道宽度和其他参数自动计算

#### 选择"以道路总宽计算"时：
- **单车道宽度 (textBox2)**: 不可编辑状态（灰色）
- **道路总宽 (textBox5)**: 可编辑状态
- **自动计算**: 单车道宽度根据道路总宽和其他参数自动计算

### 2. 计算公式

#### 以单车道宽度计算道路总宽

**同向车道数量 = 1 时：**
```
道路总宽 = 车道虚线宽度 + 单条车道宽度×2 + 车道边线宽度×2 + 非机动车道宽度×2
```

**同向车道数量 > 1 时：**
```
道路总宽 = 中心双黄线间距 + 单黄线宽度×2 + 单条车道宽度×同向车道数量×2 + 
          车道虚线宽度×(同向车道数量-1)×2 + 车道边线宽度×2 + 非机动车道宽度×2
```

#### 以道路总宽计算单车道宽度

**同向车道数量 = 1 时：**
```
单车道宽度 = (道路总宽 - 车道虚线宽度 - 车道边线宽度×2 - 非机动车道宽度×2) × 0.5
```

**同向车道数量 > 1 时：**
```
单车道宽度 = (道路总宽 - 中心双黄线间距 - 单黄线宽度×2 - 
             车道虚线宽度×(同向车道数量-1)×2 - 车道边线宽度×2 - 非机动车道宽度×2) × 0.5 ÷ 同向车道数量
```

### 3. 参数对应关系

| 参数名称 | 对应控件 | 说明 |
|---------|---------|------|
| 中心双黄线间距 | textBox1 | 双向道路中心线间距 |
| 单车道宽度 | textBox2 | 单条车道的宽度 |
| 单黄线宽度 | textBox3 | 黄色标线的宽度 |
| 同向车道数量 | textBox同向车道数 | 同一方向的车道数量 |
| 道路总宽 | textBox5 | 整条道路的总宽度 |
| 非机动车道宽度 | textBox6 | 非机动车道宽度 |
| 车道边线宽度 | textBox7 | 车道边缘线宽度 |
| 车道虚线宽度 | textBox8 | 车道分隔虚线宽度 |

### 4. 自动计算触发

以下参数变化时会自动触发重新计算：
- 中心双黄线间距 (textBox1)
- 单车道宽度 (textBox2) - 仅在"以单车道宽度计算"模式
- 单黄线宽度 (textBox3)
- 同向车道数量 (textBox同向车道数)
- 道路总宽 (textBox5) - 仅在"以道路总宽计算"模式
- 非机动车道宽度 (textBox6)
- 车道边线宽度 (textBox7)
- 车道虚线宽度 (textBox8)

### 5. 实现细节

#### 事件绑定
```csharp
private void SetupCalculationModeControls()
{
    // 绑定单选按钮事件
    this.radioButtonLaneWidth.CheckedChanged += RadioButtonLaneWidth_CheckedChanged;
    this.radioButtonTotalWidth.CheckedChanged += RadioButtonTotalWidth_CheckedChanged;
    
    // 绑定所有相关参数的TextChanged事件
    // 当任何参数变化时自动重新计算
}
```

#### 控件状态切换
```csharp
private void UpdateControlStates()
{
    if (this.radioButtonLaneWidth.Checked)
    {
        this.textBox2.Enabled = true;   // 单车道宽度可编辑
        this.textBox5.Enabled = false;  // 道路总宽不可编辑
        CalculateTotalWidth();          // 自动计算道路总宽
    }
    else if (this.radioButtonTotalWidth.Checked)
    {
        this.textBox2.Enabled = false;  // 单车道宽度不可编辑
        this.textBox5.Enabled = true;   // 道路总宽可编辑
        CalculateLaneWidth();           // 自动计算单车道宽度
    }
}
```

## 使用说明

1. **选择计算模式**: 点击相应的单选按钮选择计算模式
2. **输入参数**: 在可编辑的文本框中输入相应参数
3. **自动计算**: 系统会根据选择的模式自动计算对应的值
4. **实时更新**: 修改任何相关参数时，计算结果会实时更新

## 注意事项

- 计算结果保留2位小数
- 当计算结果为负数时，不会更新显示值
- 所有计算都包含异常处理，计算失败时保持原值
- 避免了重复事件绑定的问题
