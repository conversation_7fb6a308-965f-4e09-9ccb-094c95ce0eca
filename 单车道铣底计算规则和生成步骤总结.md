# 单车道铣底计算规则和生成步骤总结

## 📋 概述

单车道铣底是道路标线系统中的一个特殊功能，用于在满足特定条件时创建铣底区域。该功能分为两个主要步骤：基准线创建和铣底区域生成。

## 🎯 触发条件

### 严格的三重条件检查
单车道铣底功能只有在同时满足以下三个条件时才会触发：

1. **同向车道数量 = 1**
2. **用户勾选铣底选项** (`checkBoxMilling.Checked`)
3. **非机动车道宽度 >= 7.5米** (`textBox6 >= 7.5`)

```csharp
// 触发条件检查代码
if (this.checkBoxMilling.Checked && bikeLineWidth >= 7.5)
{
    // 满足所有条件，执行单车道铣底
    CreateSingleLaneMilling(...);
}
```

## 🔧 参数映射

| 参数名称 | 对应控件 | 默认值 | 说明 |
|---------|---------|--------|------|
| 车道虚线宽度 | textBox8 | 0.15 | 车道分隔虚线的宽度 |
| 单车道宽度 | textBox2 | 3.75 | 单条车道的宽度 |
| 车道边线宽度 | textBox7 | 0.15 | 车道边缘线宽度 |
| 非机动车道宽度 | textBox6 | 2.5 | 非机动车道宽度（触发条件参数） |
| 铣底宽度 | textBoxMillingWidth | 7.0 | 铣底区域的宽度 |
| 停止线宽度 | textBox13 | 0.2 | 停止线的宽度 |
| 铣底颜色 | comboBoxMillingColor | - | 用户选择的铣底颜色 |

## 📐 计算公式

### 基准线计算公式

#### 延伸长度计算
```
延伸长度 = 停止线宽度 + 铣底宽度×0.5 - 车道虚线宽度×0.5
```

#### 裁剪距离计算
```
裁剪距离 = 铣底宽度×0.5 - 车道虚线宽度×0.5
```

### 铣底区域偏移计算公式

#### 第一次偏移距离（内侧边界）
```
firstOffset = 车道虚线宽度×0.5 + 单车道宽度 + 车道边线宽度 + (铣底宽度-车道边线宽度)×0.5
```

#### 第二次偏移距离（外侧边界）
```
secondOffset = 车道虚线宽度×0.5 + 单车道宽度 + 车道边线宽度 + 非机动车道宽度 - (铣底宽度-车道边线宽度)×0.5
```

## 🔄 生成步骤详解

### 第一步：基准线创建

#### 步骤1A：创建左A基准线
1. **延伸方向**：从起始线的起始点出发，沿起始线的反向（向起始方向外侧）延伸
2. **延伸长度**：使用延伸长度公式计算
3. **连接原线**：将延伸直线与原始起始线连接，形成完整的延长线
4. **标记裁剪点**：从延长线的结束点向起始点方向标记裁剪点
5. **裁剪距离**：使用裁剪距离公式计算
6. **分割保留**：在裁剪点处分割延长线，比较两侧线段长度，保留较长的线段
7. **命名**：将保留的线段命名为"左A基准线"

#### 步骤1B：创建右A基准线
1. **延伸方向**：从起始线的结束点出发，沿起始线的正向（向结束方向外侧）延伸
2. **延伸长度**：使用延伸长度公式计算
3. **连接原线**：将延伸直线与原始起始线连接，形成完整的延长线
4. **标记裁剪点**：从延长线的起始点向结束点方向标记裁剪点
5. **裁剪距离**：使用裁剪距离公式计算
6. **分割保留**：在裁剪点处分割延长线，比较两侧线段长度，保留较长的线段
7. **命名**：将保留的线段命名为"右A基准线"

### 第二步：铣底区域创建

#### 步骤2A：创建左侧铣底区域
1. **基于左A基准线**：使用步骤1A创建的左A基准线作为基础
2. **第一次偏移**：
   - 计算第一次偏移距离
   - 向右偏移（注意：左右方向交换，使用负值）
   - 生成左B线
3. **第二次偏移**：
   - 计算第二次偏移距离
   - 向右偏移（注意：左右方向交换，使用负值）
   - 生成左C线
4. **封口连接**：连接左B和左C的端点形成闭合的铣底区域

#### 步骤2B：创建右侧铣底区域
1. **基于右A基准线**：使用步骤1B创建的右A基准线作为基础
2. **第一次偏移**：
   - 计算第一次偏移距离
   - 向左偏移（注意：左右方向交换，使用正值）
   - 生成右B线
3. **第二次偏移**：
   - 计算第二次偏移距离
   - 向左偏移（注意：左右方向交换，使用正值）
   - 生成右C线
4. **封口连接**：连接右B和右C的端点形成闭合的铣底区域

## ⚙️ 技术实现要点

### 1. AutoCAD原生API使用
- **偏移操作**：严格使用`GetOffsetCurves`方法
- **分割操作**：使用`GetSplitCurves`方法进行精确分割
- **几何延长**：使用`ExtendStartLineToPosition`方法

### 2. 几何类型支持
- **直线（Line）**：保持原有偏移方向
- **多段线（Polyline）**：左右方向交换
- **圆弧（Arc）**：左右方向交换
- **样条曲线（Spline）**：左右方向交换
- **其他曲线类型**：左右方向交换

### 3. 左右方向交换处理
```csharp
// 对于非直线几何类型，需要反转偏移方向
double actualOffsetValue = offsetValue;
if (!(baseCurve is Line))
{
    // 对于多段线、曲线、样条曲线、圆弧，左右方向相反
    actualOffsetValue = -offsetValue;
}
```

### 4. 封口连接方法
使用`CreateTwoLinesClosure`方法连接两条偏移线的端点，形成闭合的铣底区域。

## 📊 实际计算示例

### 示例参数
- 车道虚线宽度：0.15米
- 单车道宽度：3.5米
- 车道边线宽度：0.15米
- 非机动车道宽度：8.0米（满足>=7.5的触发条件）
- 铣底宽度：7.0米
- 停止线宽度：0.2米

### 计算结果
```
延伸长度 = 0.2 + 7.0×0.5 - 0.15×0.5 = 3.625米
裁剪距离 = 7.0×0.5 - 0.15×0.5 = 3.425米

第一次偏移距离 = 0.15×0.5 + 3.5 + 0.15 + (7.0-0.15)×0.5 = 7.5米
第二次偏移距离 = 0.15×0.5 + 3.5 + 0.15 + 8.0 - (7.0-0.15)×0.5 = 8.35米
```

## 🔍 验证和调试

### 调试信息输出
系统会输出详细的调试信息，包括：
- 触发条件检查结果
- 基准线创建验证（类型和长度）
- 偏移距离计算结果
- 左右方向交换提示
- 各步骤执行状态

### 验证要点
1. **触发条件**：确认三个条件都满足
2. **基准线长度**：验证延长和裁剪后的基准线长度合理
3. **偏移方向**：确认左右方向交换正确处理
4. **封口完整性**：检查铣底区域是否形成完整的闭合图形

## 📝 注意事项

1. **严格的触发条件**：只有同时满足三个条件才会执行
2. **几何特性保持**：偏移后保持原始曲线特征，不简化为直线
3. **方向处理**：非直线几何类型需要处理左右方向交换
4. **错误处理**：每个步骤都有独立的异常处理机制
5. **资源管理**：及时清理临时创建的几何对象
